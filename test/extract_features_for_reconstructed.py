#!/usr/bin/env python3
"""
为重构后的TAD数据集提取特征
使用MMAction2的特征提取工具为完整视频提取特征
"""

import json
import os
import subprocess
from pathlib import Path
import argparse
import shutil

class FeatureExtractor:
    def __init__(self, data_root="/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD",
                 mmaction2_root="/home/<USER>/johnny_ws/mmaction2_ws"):
        self.data_root = Path(data_root)
        self.mmaction2_root = Path(mmaction2_root)
        self.feature_output_dir = self.data_root / "features_reconstructed"
        
        # 创建特征输出目录
        self.feature_output_dir.mkdir(exist_ok=True)
        
    def load_reconstructed_annotations(self):
        """加载重构后的标注文件"""
        train_file = self.data_root / "multiclass_tad_train_reconstructed.json"
        val_file = self.data_root / "multiclass_tad_val_reconstructed.json"
        
        all_videos = {}
        
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
                all_videos.update(train_data)
        
        if val_file.exists():
            with open(val_file, 'r', encoding='utf-8') as f:
                val_data = json.load(f)
                all_videos.update(val_data)
        
        return all_videos
    
    def create_video_list(self, videos_info):
        """创建视频列表文件用于特征提取"""
        video_list_file = self.data_root / "video_list_for_feature_extraction.txt"
        
        with open(video_list_file, 'w') as f:
            for video_id, info in videos_info.items():
                if 'original_video_path' in info:
                    video_path = info['original_video_path']
                    if Path(video_path).exists():
                        # 格式: video_path output_name
                        output_name = f"{video_id}.csv"
                        f.write(f"{video_path} {output_name}\n")
                    else:
                        print(f"警告: 视频文件不存在: {video_path}")
        
        return video_list_file
    
    def extract_features_slowonly(self, video_list_file):
        """使用SlowOnly模型提取特征"""
        print("使用SlowOnly模型提取特征...")
        
        # SlowOnly特征提取配置
        config_file = self.mmaction2_root / "configs/recognition/slowonly/slowonly_r50_8x8x1_256e_kinetics400_rgb.py"
        checkpoint_file = self.mmaction2_root / "checkpoints/slowonly_r50_8x8x1_256e_kinetics400_rgb_20200614-de5e4b7b.pth"
        
        # 检查配置文件和检查点是否存在
        if not config_file.exists():
            print(f"配置文件不存在: {config_file}")
            return False
        
        if not checkpoint_file.exists():
            print(f"检查点文件不存在: {checkpoint_file}")
            print("请先下载SlowOnly预训练模型")
            return False
        
        # 构建特征提取命令
        cmd = [
            "python", 
            str(self.mmaction2_root / "tools/data/extract_rgb_frames.py"),
            str(video_list_file),
            str(self.feature_output_dir),
            "--config", str(config_file),
            "--checkpoint", str(checkpoint_file),
            "--modality", "RGB",
            "--task", "feature"
        ]
        
        try:
            # 切换到MMAction2目录
            original_cwd = os.getcwd()
            os.chdir(str(self.mmaction2_root))
            
            # 运行特征提取
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ SlowOnly特征提取完成")
                return True
            else:
                print(f"❌ SlowOnly特征提取失败:")
                print(f"stdout: {result.stdout}")
                print(f"stderr: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 特征提取过程出错: {e}")
            return False
        finally:
            os.chdir(original_cwd)
    
    def extract_features_custom(self, video_list_file):
        """使用自定义方法提取特征（备选方案）"""
        print("使用自定义方法提取特征...")
        
        try:
            import cv2
            import numpy as np
            import torch
            from torchvision import transforms
        except ImportError as e:
            print(f"缺少必要的依赖: {e}")
            return False
        
        # 读取视频列表
        with open(video_list_file, 'r') as f:
            video_lines = f.readlines()
        
        transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        for line in video_lines:
            video_path, output_name = line.strip().split()
            video_path = Path(video_path)
            output_path = self.feature_output_dir / output_name
            
            if output_path.exists():
                print(f"跳过已存在的特征文件: {output_name}")
                continue
            
            print(f"处理视频: {video_path.name}")
            
            # 提取视频帧
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                print(f"无法打开视频: {video_path}")
                continue
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 计算采样间隔（目标：每秒10帧特征）
            target_fps = 10
            sample_interval = max(1, int(fps / target_fps))
            
            features = []
            frame_idx = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_idx % sample_interval == 0:
                    # 转换为RGB
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    
                    # 简单的特征提取（可以替换为更复杂的方法）
                    # 这里使用颜色直方图和纹理特征作为示例
                    hist_r = cv2.calcHist([frame_rgb], [0], None, [32], [0, 256])
                    hist_g = cv2.calcHist([frame_rgb], [1], None, [32], [0, 256])
                    hist_b = cv2.calcHist([frame_rgb], [2], None, [32], [0, 256])
                    
                    # 组合特征
                    feature_vector = np.concatenate([
                        hist_r.flatten(),
                        hist_g.flatten(), 
                        hist_b.flatten()
                    ])
                    
                    features.append(feature_vector)
                
                frame_idx += 1
            
            cap.release()
            
            if features:
                # 保存特征
                features_array = np.array(features)
                np.savetxt(output_path, features_array, delimiter=',')
                print(f"  保存特征: {output_path} (形状: {features_array.shape})")
            else:
                print(f"  警告: 未提取到特征: {video_path}")
        
        return True
    
    def update_annotation_files(self, videos_info):
        """更新标注文件中的特征路径"""
        print("更新标注文件中的特征路径...")
        
        # 更新训练集
        train_file = self.data_root / "multiclass_tad_train_reconstructed.json"
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
            
            for video_id in train_data.keys():
                if video_id in videos_info:
                    feature_file = self.feature_output_dir / f"{video_id}.csv"
                    if feature_file.exists():
                        # 更新特征帧数
                        feature_array = np.loadtxt(feature_file, delimiter=',')
                        train_data[video_id]['feature_frame'] = len(feature_array)
                        train_data[video_id]['feature_path'] = str(feature_file)
            
            with open(train_file, 'w', encoding='utf-8') as f:
                json.dump(train_data, f, indent=2, ensure_ascii=False)
        
        # 更新验证集
        val_file = self.data_root / "multiclass_tad_val_reconstructed.json"
        if val_file.exists():
            with open(val_file, 'r', encoding='utf-8') as f:
                val_data = json.load(f)
            
            for video_id in val_data.keys():
                if video_id in videos_info:
                    feature_file = self.feature_output_dir / f"{video_id}.csv"
                    if feature_file.exists():
                        # 更新特征帧数
                        feature_array = np.loadtxt(feature_file, delimiter=',')
                        val_data[video_id]['feature_frame'] = len(feature_array)
                        val_data[video_id]['feature_path'] = str(feature_file)
            
            with open(val_file, 'w', encoding='utf-8') as f:
                json.dump(val_data, f, indent=2, ensure_ascii=False)
        
        print("✅ 标注文件更新完成")
    
    def run(self, method='custom'):
        """运行特征提取流程"""
        print("开始为重构后的数据集提取特征...")
        
        # 1. 加载重构后的标注
        print("1. 加载重构后的标注文件...")
        videos_info = self.load_reconstructed_annotations()
        
        if not videos_info:
            print("❌ 未找到重构后的标注文件")
            return False
        
        print(f"   发现 {len(videos_info)} 个视频需要提取特征")
        
        # 2. 创建视频列表
        print("2. 创建视频列表...")
        video_list_file = self.create_video_list(videos_info)
        print(f"   视频列表已保存: {video_list_file}")
        
        # 3. 提取特征
        print("3. 提取特征...")
        if method == 'slowonly':
            success = self.extract_features_slowonly(video_list_file)
        else:
            success = self.extract_features_custom(video_list_file)
        
        if not success:
            print("❌ 特征提取失败")
            return False
        
        # 4. 更新标注文件
        print("4. 更新标注文件...")
        self.update_annotation_files(videos_info)
        
        print("✅ 特征提取完成!")
        print(f"   特征文件保存在: {self.feature_output_dir}")
        
        return True

def main():
    parser = argparse.ArgumentParser(description='为重构后的TAD数据集提取特征')
    parser.add_argument('--data-root',
                       default='/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD',
                       help='数据集根目录')
    parser.add_argument('--mmaction2-root',
                       default='/home/<USER>/johnny_ws/mmaction2_ws',
                       help='MMAction2根目录')
    parser.add_argument('--method',
                       choices=['slowonly', 'custom'],
                       default='custom',
                       help='特征提取方法')
    
    args = parser.parse_args()
    
    extractor = FeatureExtractor(
        data_root=args.data_root,
        mmaction2_root=args.mmaction2_root
    )
    
    success = extractor.run(method=args.method)
    return success

if __name__ == "__main__":
    import numpy as np
    success = main()
    exit(0 if success else 1)

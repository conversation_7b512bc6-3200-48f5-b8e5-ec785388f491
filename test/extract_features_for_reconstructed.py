#!/usr/bin/env python3
"""
为重构后的TAD数据集提取特征
使用MMAction2的特征提取工具为完整视频提取特征
"""

import json
import os
import subprocess
from pathlib import Path
import argparse
import shutil

class FeatureExtractor:
    def __init__(self, data_root="/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD",
                 mmaction2_root="/home/<USER>/johnny_ws/mmaction2_ws"):
        self.data_root = Path(data_root)
        self.mmaction2_root = Path(mmaction2_root)
        self.feature_output_dir = self.data_root / "features_reconstructed"
        
        # 创建特征输出目录
        self.feature_output_dir.mkdir(exist_ok=True)
        
    def load_reconstructed_annotations(self):
        """加载重构后的标注文件"""
        train_file = self.data_root / "multiclass_tad_train_reconstructed.json"
        val_file = self.data_root / "multiclass_tad_val_reconstructed.json"
        
        all_videos = {}
        
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
                all_videos.update(train_data)
        
        if val_file.exists():
            with open(val_file, 'r', encoding='utf-8') as f:
                val_data = json.load(f)
                all_videos.update(val_data)
        
        return all_videos
    
    def create_video_list(self, videos_info):
        """创建视频列表文件用于特征提取"""
        video_list_file = self.data_root / "video_list_for_feature_extraction.txt"
        
        with open(video_list_file, 'w') as f:
            for video_id, info in videos_info.items():
                if 'original_video_path' in info:
                    video_path = info['original_video_path']
                    if Path(video_path).exists():
                        # 格式: video_path output_name
                        output_name = f"{video_id}.csv"
                        f.write(f"{video_path} {output_name}\n")
                    else:
                        print(f"警告: 视频文件不存在: {video_path}")
        
        return video_list_file
    
    def extract_features_slowonly(self, video_list_file):
        """使用SlowOnly模型提取特征"""
        print("使用SlowOnly模型提取特征...")

        try:
            import torch
            import torch.nn as nn
            import numpy as np
            import cv2
        except ImportError as e:
            print(f"缺少必要的依赖: {e}")
            print("请确保已安装PyTorch和OpenCV")
            return False

        # SlowOnly配置
        config_file = self.mmaction2_root / "configs/recognition/slowonly/slowonly_r50_8x8x1_256e_kinetics400_rgb.py"
        checkpoint_file = self.mmaction2_root / "checkpoints/slowonly_r50_8x8x1_256e_kinetics400_rgb_20200614-de5e4b7b.pth"

        # 检查文件是否存在
        if not config_file.exists():
            print(f"配置文件不存在: {config_file}")
            print("尝试使用内置配置...")
            return self.extract_features_slowonly_builtin(video_list_file)

        if not checkpoint_file.exists():
            print(f"检查点文件不存在: {checkpoint_file}")
            print("请下载SlowOnly预训练模型或使用内置特征提取")
            return self.extract_features_slowonly_builtin(video_list_file)

        try:
            # 初始化模型
            model = init_recognizer(str(config_file), str(checkpoint_file), device='cpu')

            # 读取视频列表
            with open(video_list_file, 'r') as f:
                video_lines = f.readlines()

            for line in video_lines:
                video_path, output_name = line.strip().split()
                video_path = Path(video_path)
                output_path = self.feature_output_dir / output_name

                if output_path.exists():
                    print(f"跳过已存在的特征文件: {output_name}")
                    continue

                print(f"处理视频: {video_path.name}")

                # 提取特征
                features = self.extract_video_features_with_model(model, video_path)

                if features is not None:
                    # 保存特征
                    np.savetxt(output_path, features, delimiter=',')
                    print(f"  保存特征: {output_path} (形状: {features.shape})")
                else:
                    print(f"  警告: 特征提取失败: {video_path}")

            return True

        except Exception as e:
            print(f"SlowOnly特征提取失败: {e}")
            print("回退到内置特征提取方法...")
            return self.extract_features_slowonly_builtin(video_list_file)
    
    def extract_video_features_with_model(self, model, video_path):
        """使用SlowOnly模型提取单个视频的特征"""
        try:
            import torch
            import cv2
            import numpy as np
            from torchvision import transforms

            # 视频预处理配置
            transform = transforms.Compose([
                transforms.ToPILImage(),
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                   std=[0.229, 0.224, 0.225])
            ])

            # 读取视频
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                return None

            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # 采样帧（每秒10帧）
            target_fps = 10
            sample_interval = max(1, int(fps / target_fps))

            frames = []
            frame_idx = 0

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                if frame_idx % sample_interval == 0:
                    # 转换为RGB
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    frames.append(frame_rgb)

                frame_idx += 1

            cap.release()

            if len(frames) == 0:
                return None

            # 使用模型提取特征
            features = []

            # 分批处理帧以避免内存问题
            batch_size = 8
            for i in range(0, len(frames), batch_size):
                batch_frames = frames[i:i+batch_size]

                # 预处理帧
                batch_tensors = []
                for frame in batch_frames:
                    tensor = transform(frame)
                    batch_tensors.append(tensor)

                if batch_tensors:
                    batch_input = torch.stack(batch_tensors).unsqueeze(0)  # [1, batch_size, 3, 224, 224]

                    with torch.no_grad():
                        # 提取特征（使用模型的backbone）
                        if hasattr(model.backbone, 'extract_feat'):
                            feat = model.backbone.extract_feat(batch_input)
                        else:
                            feat = model.backbone(batch_input)

                        # 全局平均池化
                        if len(feat.shape) > 2:
                            feat = torch.mean(feat, dim=list(range(2, len(feat.shape))))

                        features.extend(feat.cpu().numpy())

            return np.array(features) if features else None

        except Exception as e:
            print(f"特征提取错误: {e}")
            return None

    def extract_features_slowonly_builtin(self, video_list_file):
        """内置的SlowOnly风格特征提取（备选方案）"""
        print("使用内置SlowOnly风格特征提取...")

        try:
            import torch
            import torch.nn as nn
            import torchvision.models as models
            from torchvision import transforms
            import cv2
            import numpy as np
        except ImportError as e:
            print(f"缺少必要的依赖: {e}")
            return False

        # 使用预训练的ResNet50作为特征提取器
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")

        # 加载预训练模型
        backbone = models.resnet50(pretrained=True)
        backbone = nn.Sequential(*list(backbone.children())[:-1])  # 移除最后的分类层
        backbone.eval()
        backbone.to(device)

        # 预处理配置
        transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                               std=[0.229, 0.224, 0.225])
        ])

        # 读取视频列表
        with open(video_list_file, 'r') as f:
            video_lines = f.readlines()

        for line in video_lines:
            video_path, output_name = line.strip().split()
            video_path = Path(video_path)
            output_path = self.feature_output_dir / output_name

            if output_path.exists():
                print(f"跳过已存在的特征文件: {output_name}")
                continue

            print(f"处理视频: {video_path.name}")

            # 提取视频帧
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                print(f"无法打开视频: {video_path}")
                continue

            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # 计算采样间隔（目标：每秒10帧特征）
            target_fps = 10
            sample_interval = max(1, int(fps / target_fps))

            features = []
            frame_idx = 0

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                if frame_idx % sample_interval == 0:
                    # 转换为RGB
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                    # 预处理
                    frame_tensor = transform(frame_rgb).unsqueeze(0).to(device)

                    # 提取特征
                    with torch.no_grad():
                        feat = backbone(frame_tensor)
                        feat = feat.squeeze().cpu().numpy()  # [2048]
                        features.append(feat)

                frame_idx += 1

            cap.release()

            if features:
                # 保存特征
                features_array = np.array(features)
                np.savetxt(output_path, features_array, delimiter=',')
                print(f"  保存特征: {output_path} (形状: {features_array.shape})")
            else:
                print(f"  警告: 未提取到特征: {video_path}")

        return True

    def extract_features_custom(self, video_list_file):
        """使用自定义方法提取特征（备选方案）"""
        print("使用自定义方法提取特征...")
        
        try:
            import cv2
            import numpy as np
            import torch
            from torchvision import transforms
        except ImportError as e:
            print(f"缺少必要的依赖: {e}")
            return False
        
        # 读取视频列表
        with open(video_list_file, 'r') as f:
            video_lines = f.readlines()
        
        transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        for line in video_lines:
            video_path, output_name = line.strip().split()
            video_path = Path(video_path)
            output_path = self.feature_output_dir / output_name
            
            if output_path.exists():
                print(f"跳过已存在的特征文件: {output_name}")
                continue
            
            print(f"处理视频: {video_path.name}")
            
            # 提取视频帧
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                print(f"无法打开视频: {video_path}")
                continue
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 计算采样间隔（目标：每秒10帧特征）
            target_fps = 10
            sample_interval = max(1, int(fps / target_fps))
            
            features = []
            frame_idx = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_idx % sample_interval == 0:
                    # 转换为RGB
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    
                    # 简单的特征提取（可以替换为更复杂的方法）
                    # 这里使用颜色直方图和纹理特征作为示例
                    hist_r = cv2.calcHist([frame_rgb], [0], None, [32], [0, 256])
                    hist_g = cv2.calcHist([frame_rgb], [1], None, [32], [0, 256])
                    hist_b = cv2.calcHist([frame_rgb], [2], None, [32], [0, 256])
                    
                    # 组合特征
                    feature_vector = np.concatenate([
                        hist_r.flatten(),
                        hist_g.flatten(), 
                        hist_b.flatten()
                    ])
                    
                    features.append(feature_vector)
                
                frame_idx += 1
            
            cap.release()
            
            if features:
                # 保存特征
                features_array = np.array(features)
                np.savetxt(output_path, features_array, delimiter=',')
                print(f"  保存特征: {output_path} (形状: {features_array.shape})")
            else:
                print(f"  警告: 未提取到特征: {video_path}")
        
        return True
    
    def update_annotation_files(self, videos_info):
        """更新标注文件中的特征路径"""
        print("更新标注文件中的特征路径...")
        
        # 更新训练集
        train_file = self.data_root / "multiclass_tad_train_reconstructed.json"
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
            
            for video_id in train_data.keys():
                if video_id in videos_info:
                    feature_file = self.feature_output_dir / f"{video_id}.csv"
                    if feature_file.exists():
                        # 更新特征帧数
                        feature_array = np.loadtxt(feature_file, delimiter=',')
                        train_data[video_id]['feature_frame'] = len(feature_array)
                        train_data[video_id]['feature_path'] = str(feature_file)
            
            with open(train_file, 'w', encoding='utf-8') as f:
                json.dump(train_data, f, indent=2, ensure_ascii=False)
        
        # 更新验证集
        val_file = self.data_root / "multiclass_tad_val_reconstructed.json"
        if val_file.exists():
            with open(val_file, 'r', encoding='utf-8') as f:
                val_data = json.load(f)
            
            for video_id in val_data.keys():
                if video_id in videos_info:
                    feature_file = self.feature_output_dir / f"{video_id}.csv"
                    if feature_file.exists():
                        # 更新特征帧数
                        feature_array = np.loadtxt(feature_file, delimiter=',')
                        val_data[video_id]['feature_frame'] = len(feature_array)
                        val_data[video_id]['feature_path'] = str(feature_file)
            
            with open(val_file, 'w', encoding='utf-8') as f:
                json.dump(val_data, f, indent=2, ensure_ascii=False)
        
        print("✅ 标注文件更新完成")
    
    def run(self, method='custom'):
        """运行特征提取流程"""
        print("开始为重构后的数据集提取特征...")
        
        # 1. 加载重构后的标注
        print("1. 加载重构后的标注文件...")
        videos_info = self.load_reconstructed_annotations()
        
        if not videos_info:
            print("❌ 未找到重构后的标注文件")
            return False
        
        print(f"   发现 {len(videos_info)} 个视频需要提取特征")
        
        # 2. 创建视频列表
        print("2. 创建视频列表...")
        video_list_file = self.create_video_list(videos_info)
        print(f"   视频列表已保存: {video_list_file}")
        
        # 3. 提取特征
        print("3. 提取特征...")
        if method == 'slowonly':
            success = self.extract_features_slowonly(video_list_file)
        else:
            success = self.extract_features_custom(video_list_file)
        
        if not success:
            print("❌ 特征提取失败")
            return False
        
        # 4. 更新标注文件
        print("4. 更新标注文件...")
        self.update_annotation_files(videos_info)
        
        print("✅ 特征提取完成!")
        print(f"   特征文件保存在: {self.feature_output_dir}")
        
        return True

def main():
    parser = argparse.ArgumentParser(description='为重构后的TAD数据集提取特征')
    parser.add_argument('--data-root',
                       default='/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD',
                       help='数据集根目录')
    parser.add_argument('--mmaction2-root',
                       default='/home/<USER>/johnny_ws/mmaction2_ws',
                       help='MMAction2根目录')
    parser.add_argument('--method',
                       choices=['slowonly', 'custom'],
                       default='custom',
                       help='特征提取方法')
    
    args = parser.parse_args()
    
    extractor = FeatureExtractor(
        data_root=args.data_root,
        mmaction2_root=args.mmaction2_root
    )
    
    success = extractor.run(method=args.method)
    return success

if __name__ == "__main__":
    import numpy as np
    success = main()
    exit(0 if success else 1)

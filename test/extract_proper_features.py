#!/usr/bin/env python3
"""
提取正确的深度学习特征
使用预训练的ResNet50提取标准化的特征，替换异常的颜色直方图特征
"""

import os
import json
import numpy as np
import cv2
from pathlib import Path
import torch
import torch.nn as nn
import torchvision.models as models
from torchvision import transforms
from tqdm import tqdm

class ProperFeatureExtractor:
    def __init__(self, data_root="/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD"):
        self.data_root = Path(data_root)
        self.features_dir = self.data_root / "features_slowonly_reconstructed"
        self.features_dir.mkdir(exist_ok=True)
        
        # 设置设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 加载预训练的ResNet50模型
        self.model = self._load_model()
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
    
    def _load_model(self):
        """加载预训练的ResNet50模型作为特征提取器"""
        print("加载预训练的ResNet50模型...")
        
        # 加载预训练模型
        resnet = models.resnet50(pretrained=True)
        
        # 移除最后的分类层，保留特征提取部分
        feature_extractor = nn.Sequential(*list(resnet.children())[:-1])
        
        # 设置为评估模式
        feature_extractor.eval()
        feature_extractor.to(self.device)
        
        print("模型加载完成")
        return feature_extractor
    
    def extract_video_features(self, video_path, target_frames=100):
        """从视频中提取特征"""
        print(f"处理视频: {Path(video_path).name}")
        
        # 打开视频
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            print(f"无法打开视频: {video_path}")
            return None
        
        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        
        print(f"  视频信息: {frame_count}帧, {fps:.2f}fps, {duration:.2f}秒")
        
        # 计算采样间隔，确保提取到target_frames个特征
        if frame_count <= target_frames:
            sample_interval = 1
        else:
            sample_interval = frame_count // target_frames
        
        features = []
        frame_idx = 0
        extracted_count = 0
        
        with torch.no_grad():
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 按间隔采样帧
                if frame_idx % sample_interval == 0 and extracted_count < target_frames:
                    # 转换为RGB
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    
                    # 预处理
                    frame_tensor = self.transform(frame_rgb).unsqueeze(0).to(self.device)
                    
                    # 提取特征
                    feature = self.model(frame_tensor)
                    feature = feature.squeeze().cpu().numpy()  # [2048]
                    
                    features.append(feature)
                    extracted_count += 1
                
                frame_idx += 1
        
        cap.release()
        
        if not features:
            print(f"  警告: 未提取到特征")
            return None
        
        # 转换为numpy数组
        features_array = np.array(features)
        
        # 如果提取的特征少于目标数量，进行插值
        if len(features_array) < target_frames:
            print(f"  插值特征: {len(features_array)} -> {target_frames}")
            features_array = self._interpolate_features(features_array, target_frames)
        elif len(features_array) > target_frames:
            # 如果提取的特征多于目标数量，进行下采样
            print(f"  下采样特征: {len(features_array)} -> {target_frames}")
            indices = np.linspace(0, len(features_array) - 1, target_frames, dtype=int)
            features_array = features_array[indices]
        
        print(f"  最终特征形状: {features_array.shape}")
        print(f"  特征值范围: {features_array.min():.6f} ~ {features_array.max():.6f}")
        
        return features_array
    
    def _interpolate_features(self, features, target_length):
        """对特征进行插值以达到目标长度"""
        from scipy import interpolate
        
        current_length = len(features)
        feature_dim = features.shape[1]
        
        # 创建插值函数
        x_old = np.linspace(0, 1, current_length)
        x_new = np.linspace(0, 1, target_length)
        
        interpolated_features = np.zeros((target_length, feature_dim))
        
        for i in range(feature_dim):
            f = interpolate.interp1d(x_old, features[:, i], kind='linear')
            interpolated_features[:, i] = f(x_new)
        
        return interpolated_features
    
    def process_all_videos(self):
        """处理所有重构数据集中的视频"""
        print("开始提取正确的深度学习特征...")
        
        # 加载重构后的标注文件
        train_file = self.data_root / "multiclass_tad_train_reconstructed.json"
        val_file = self.data_root / "multiclass_tad_val_reconstructed.json"
        
        all_videos = {}
        
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
                all_videos.update(train_data)
        
        if val_file.exists():
            with open(val_file, 'r', encoding='utf-8') as f:
                val_data = json.load(f)
                all_videos.update(val_data)
        
        print(f"找到 {len(all_videos)} 个视频需要处理")
        
        success_count = 0
        
        for video_id, info in tqdm(all_videos.items(), desc="提取特征"):
            if 'original_video_path' not in info:
                continue
            
            video_path = info['original_video_path']
            if not Path(video_path).exists():
                print(f"视频文件不存在: {video_path}")
                continue
            
            # 提取特征
            features = self.extract_video_features(video_path, target_frames=100)
            
            if features is not None:
                # 保存特征
                csv_file = self.features_dir / f"{video_id}.csv"
                np.savetxt(csv_file, features, delimiter=',', fmt='%.6f')
                print(f"  保存特征: {csv_file}")
                success_count += 1
            else:
                print(f"  特征提取失败: {video_id}")
        
        print(f"\n特征提取完成: {success_count}/{len(all_videos)} 个视频成功")
        return success_count > 0
    
    def update_annotation_files(self):
        """更新标注文件中的特征路径"""
        print("更新标注文件中的特征路径...")
        
        # 更新训练集
        train_file = self.data_root / "multiclass_tad_train_reconstructed.json"
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
            
            for video_id in train_data.keys():
                feature_file = self.features_dir / f"{video_id}.csv"
                if feature_file.exists():
                    train_data[video_id]['feature_frame'] = 100
                    train_data[video_id]['feature_path'] = str(feature_file)
            
            with open(train_file, 'w', encoding='utf-8') as f:
                json.dump(train_data, f, indent=2, ensure_ascii=False)
        
        # 更新验证集
        val_file = self.data_root / "multiclass_tad_val_reconstructed.json"
        if val_file.exists():
            with open(val_file, 'r', encoding='utf-8') as f:
                val_data = json.load(f)
            
            for video_id in val_data.keys():
                feature_file = self.features_dir / f"{video_id}.csv"
                if feature_file.exists():
                    val_data[video_id]['feature_frame'] = 100
                    val_data[video_id]['feature_path'] = str(feature_file)
            
            with open(val_file, 'w', encoding='utf-8') as f:
                json.dump(val_data, f, indent=2, ensure_ascii=False)
        
        print("✅ 标注文件更新完成")
    
    def validate_features(self):
        """验证生成的特征文件"""
        print("验证特征文件...")
        
        csv_files = list(self.features_dir.glob("*.csv"))
        print(f"生成的特征文件数量: {len(csv_files)}")
        
        if csv_files:
            # 检查特征文件格式
            sample_file = csv_files[0]
            try:
                features = np.loadtxt(sample_file, delimiter=',')
                print(f"特征文件验证: {sample_file.name}")
                print(f"  特征形状: {features.shape}")
                print(f"  特征值范围: {features.min():.6f} ~ {features.max():.6f}")
                
                # 检查是否是合理的深度学习特征值
                if -10 <= features.min() and features.max() <= 10:
                    print("  ✅ 特征值范围正常（标准深度学习特征）")
                else:
                    print("  ⚠️ 特征值范围可能异常")
                
                if features.shape == (100, 2048):
                    print("  ✅ 特征维度正确 (100帧 x 2048维)")
                else:
                    print(f"  ⚠️ 特征维度异常，期望 (100, 2048)，实际 {features.shape}")
                    
            except Exception as e:
                print(f"  ❌ 特征文件格式错误: {e}")
        
        return len(csv_files)
    
    def run(self):
        """运行完整的特征提取流程"""
        print("开始提取正确的深度学习特征...")
        
        # 1. 处理所有视频
        if not self.process_all_videos():
            print("❌ 特征提取失败")
            return False
        
        # 2. 更新标注文件
        self.update_annotation_files()
        
        # 3. 验证特征
        feature_count = self.validate_features()
        
        print(f"\n✅ 特征提取完成!")
        print(f"   生成了 {feature_count} 个特征文件")
        print(f"   特征保存在: {self.features_dir}")
        print(f"   特征格式: 100帧 x 2048维 ResNet50特征")
        
        return True

def main():
    extractor = ProperFeatureExtractor()
    success = extractor.run()
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

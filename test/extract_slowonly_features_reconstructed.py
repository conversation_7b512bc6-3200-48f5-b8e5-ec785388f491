#!/usr/bin/env python3
"""
为重构后的TAD数据集提取SlowOnly特征
复用原始的特征提取脚本和配置
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
import json
import numpy as np
import pickle
import scipy.interpolate

class SlowOnlyFeatureExtractor:
    def __init__(self, data_root="/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD",
                 mmaction2_root="/home/<USER>/johnny_ws/mmaction2_ws"):
        self.data_root = Path(data_root)
        self.mmaction2_root = Path(mmaction2_root)
        self.feature_extraction_dir = self.mmaction2_root / "data_process/preprocessing/feature_extraction"
        
        # 输出目录
        self.raw_features_dir = self.data_root / "features_slowonly_raw_reconstructed"
        self.processed_features_dir = self.data_root / "features_slowonly_reconstructed"
        
        # 创建输出目录
        self.raw_features_dir.mkdir(exist_ok=True)
        self.processed_features_dir.mkdir(exist_ok=True)
    
    def create_video_list_for_original_videos(self):
        """为原始完整视频创建视频列表文件"""
        # 加载重构后的标注文件
        train_file = self.data_root / "multiclass_tad_train_reconstructed.json"
        val_file = self.data_root / "multiclass_tad_val_reconstructed.json"
        
        all_videos = {}
        
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
                all_videos.update(train_data)
        
        if val_file.exists():
            with open(val_file, 'r', encoding='utf-8') as f:
                val_data = json.load(f)
                all_videos.update(val_data)
        
        # 创建视频列表文件
        video_list_file = self.data_root / "video_list_reconstructed.txt"
        
        with open(video_list_file, 'w') as f:
            for video_id, info in all_videos.items():
                if 'original_video_path' in info:
                    video_path = info['original_video_path']
                    if Path(video_path).exists():
                        # 格式: video_name label (使用0作为虚拟标签)
                        f.write(f"{Path(video_path).name} 0\n")
                    else:
                        print(f"警告: 视频文件不存在: {video_path}")
        
        print(f"视频列表已创建: {video_list_file}")
        print(f"包含 {len(all_videos)} 个视频")
        return video_list_file
    
    def download_slowonly_checkpoint(self):
        """下载SlowOnly预训练模型"""
        checkpoint_url = ('https://download.openmmlab.com/mmaction/v1.0/recognition/slowonly/'
                         'slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_kinetics400-'
                         'rgb/slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_'
                         'kinetics400-rgb_20220901-e7b65fad.pth')
        
        checkpoint_dir = self.mmaction2_root / 'work_dirs/checkpoints'
        checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        checkpoint_path = checkpoint_dir / 'slowonly_r50_kinetics400.pth'
        
        if not checkpoint_path.exists():
            print(f"下载SlowOnly R50预训练模型...")
            cmd = f"wget -O {checkpoint_path} {checkpoint_url}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"下载失败: {result.stderr}")
                return None
            print(f"模型下载完成: {checkpoint_path}")
        else:
            print(f"使用已存在的模型: {checkpoint_path}")
        
        return str(checkpoint_path)
    
    def create_feature_extraction_config(self):
        """创建适用于重构数据集的特征提取配置"""
        config_content = f'''# SlowOnly R50 特征提取配置文件 - 重构数据集版本
_base_ = [
    '{self.mmaction2_root}/mmaction2/configs/_base_/models/slowonly_r50.py',
    '{self.mmaction2_root}/mmaction2/configs/_base_/default_runtime.py'
]

# 模型设置
model = dict(
    type='Recognizer3D',
    backbone=dict(
        type='ResNet3dSlowOnly',
        depth=50,
        pretrained=None,  # 将通过checkpoint加载
        lateral=False,
        conv1_kernel=(1, 7, 7),
        conv1_stride_t=1,
        pool1_stride_t=1,
        inflate=(0, 0, 1, 1),
        norm_eval=False),
    cls_head=dict(
        type='I3DHead',
        in_channels=2048,
        num_classes=400,
        spatial_type='avg',
        dropout_ratio=0.5,
        average_clips='prob'),
    data_preprocessor=dict(
        type='ActionDataPreprocessor',
        mean=[123.675, 116.28, 103.53],
        std=[58.395, 57.12, 57.375],
        format_shape='NCTHW'))

# 数据集设置
dataset_type = 'VideoDataset'
data_root = '/home/<USER>/johnny_ws/mmaction2_ws/data/raw_videos'
ann_file = '{self.data_root}/video_list_reconstructed.txt'

file_client_args = dict(io_backend='disk')

# 测试数据管道
test_pipeline = [
    dict(type='DecordInit', **file_client_args),
    dict(
        type='SampleFrames',
        clip_len=8,
        frame_interval=8,
        num_clips=1,
        test_mode=True),
    dict(type='DecordDecode'),
    dict(type='Resize', scale=(-1, 256)),
    dict(type='CenterCrop', crop_size=224),
    dict(type='FormatShape', input_format='NCTHW'),
    dict(type='PackActionInputs')
]

# 测试数据加载器
test_dataloader = dict(
    batch_size=1,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file,
        data_prefix=dict(video=data_root),
        pipeline=test_pipeline,
        test_mode=True))

test_cfg = dict(type='TestLoop')

# 默认钩子
default_hooks = dict(
    runtime_info=dict(type='RuntimeInfoHook'),
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=20, ignore_last=False),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=3, save_best='auto'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'))

# 环境设置
env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0),
    dist_cfg=dict(backend='nccl'))

# 日志设置
vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(type='ActionVisualizer', vis_backends=vis_backends)
log_processor = dict(type='LogProcessor', window_size=20, by_epoch=True)
log_level = 'INFO'

load_from = None
resume = False
work_dir = './work_dirs/slowonly_feature_extraction_reconstructed'
'''
        
        config_file = self.data_root / "slowonly_r50_feature_extraction_reconstructed.py"
        with open(config_file, 'w') as f:
            f.write(config_content)
        
        print(f"特征提取配置已创建: {config_file}")
        return config_file
    
    def extract_raw_features(self):
        """提取原始SlowOnly特征"""
        print("开始提取SlowOnly特征...")
        
        # 创建视频列表
        video_list_file = self.create_video_list_for_original_videos()
        
        # 创建配置文件
        config_file = self.create_feature_extraction_config()
        
        # 下载预训练模型
        checkpoint_path = self.download_slowonly_checkpoint()
        if checkpoint_path is None:
            print("无法下载预训练模型，退出")
            return False
        
        # 构建特征提取命令
        cmd = [
            'python', str(self.mmaction2_root / 'mmaction2/tools/misc/clip_feature_extraction.py'),
            str(config_file),
            checkpoint_path,
            str(self.raw_features_dir),
            '--video-list', str(video_list_file),
            '--video-root', str(self.data_root),
            '--long-video-mode',
            '--clip-interval', '16',
            '--frame-interval', '1',
            '--spatial-type', 'avg',
            '--temporal-type', 'keep'
        ]
        
        print("执行特征提取命令:")
        print(f"  {' '.join(cmd)}")
        
        # 执行特征提取
        try:
            # 切换到MMAction2目录
            original_cwd = os.getcwd()
            os.chdir(str(self.mmaction2_root))
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ SlowOnly特征提取完成!")
                if result.stdout:
                    print("输出:", result.stdout)
                return True
            else:
                print("❌ SlowOnly特征提取失败!")
                print("错误:", result.stderr)
                if result.stdout:
                    print("输出:", result.stdout)
                return False
                
        except Exception as e:
            print(f"执行特征提取时出错: {e}")
            return False
        finally:
            os.chdir(original_cwd)
    
    def pool_feature_to_100_steps(self, data, num_proposals=100, num_sample_bins=3, pool_type='mean'):
        """将任意长度的特征池化到100个时间步"""
        if len(data) == 1:
            return np.tile(data, (num_proposals, 1))
        
        x_range = list(range(len(data)))
        f = scipy.interpolate.interp1d(x_range, data, axis=0, kind='linear')
        
        eps = 1e-4
        start, end = eps, len(data) - 1 - eps
        anchor_size = (end - start) / num_proposals
        
        feature = []
        ptr = start
        
        for _ in range(num_proposals):
            x_new = [ptr + i / num_sample_bins * anchor_size for i in range(num_sample_bins)]
            y_new = f(x_new)
            
            if pool_type == 'mean':
                y_new = np.mean(y_new, axis=0)
            elif pool_type == 'max':
                y_new = np.max(y_new, axis=0)
            else:
                raise NotImplementedError(f'不支持的池化类型: {pool_type}')
            
            feature.append(y_new)
            ptr += anchor_size
        
        feature = np.stack(feature)
        return feature

    def process_single_video_feature(self, pkl_file, video_id):
        """处理单个视频的特征文件"""
        try:
            # 加载特征
            with open(pkl_file, 'rb') as f:
                features = pickle.load(f)

            # 处理特征格式
            if isinstance(features, list):
                features = features[0]

            if hasattr(features, 'cpu'):  # PyTorch tensor
                features = features.cpu().numpy()

            if not isinstance(features, np.ndarray):
                print(f"警告: {pkl_file} 中的特征格式不正确，跳过")
                return False

            print(f"处理 {video_id}: 原始特征形状 {features.shape}")

            # 处理特征维度
            if len(features.shape) == 3:
                # (batch, feature_dim, time_steps)
                features = features.mean(axis=0)  # 平均所有batch
                features = features.T  # 转置为 (time_steps, feature_dim)
            elif len(features.shape) == 2:
                if features.shape[0] == 2048:
                    features = features.T  # (feature_dim, time_steps) -> (time_steps, feature_dim)

            print(f"池化前特征形状: {features.shape}")

            # 检查特征维度
            if features.shape[1] != 2048:
                print(f"警告: 特征维度是 {features.shape[1]}，期望2048")

            # 池化到100个时间步
            pooled_features = self.pool_feature_to_100_steps(features, num_proposals=100)
            print(f"池化后特征形状: {pooled_features.shape}")

            # 保存为CSV格式
            csv_file = self.processed_features_dir / f"{video_id}.csv"
            np.savetxt(csv_file, pooled_features, delimiter=',')

            print(f"保存CSV文件: {csv_file}")
            return True

        except Exception as e:
            print(f"处理 {pkl_file} 时出错: {e}")
            return False

    def postprocess_features(self):
        """后处理所有特征文件"""
        print("开始后处理SlowOnly特征...")

        # 查找所有pkl文件
        pkl_files = list(self.raw_features_dir.rglob('*.pkl'))

        if not pkl_files:
            print(f"在 {self.raw_features_dir} 中没有找到pkl文件")
            return False

        print(f"找到 {len(pkl_files)} 个特征文件")

        success_count = 0
        for pkl_file in pkl_files:
            # 从文件路径提取视频ID
            video_id = pkl_file.stem
            if video_id.endswith('.mp4'):
                video_id = video_id[:-4]

            if self.process_single_video_feature(str(pkl_file), video_id):
                success_count += 1

        print(f"后处理完成: {success_count}/{len(pkl_files)} 个文件成功")
        return success_count > 0

    def update_annotation_files(self):
        """更新标注文件中的特征路径"""
        print("更新标注文件中的特征路径...")

        # 更新训练集
        train_file = self.data_root / "multiclass_tad_train_reconstructed.json"
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)

            for video_id in train_data.keys():
                feature_file = self.processed_features_dir / f"{video_id}.csv"
                if feature_file.exists():
                    train_data[video_id]['feature_frame'] = 100  # 固定100帧
                    train_data[video_id]['feature_path'] = str(feature_file)

            with open(train_file, 'w', encoding='utf-8') as f:
                json.dump(train_data, f, indent=2, ensure_ascii=False)

        # 更新验证集
        val_file = self.data_root / "multiclass_tad_val_reconstructed.json"
        if val_file.exists():
            with open(val_file, 'r', encoding='utf-8') as f:
                val_data = json.load(f)

            for video_id in val_data.keys():
                feature_file = self.processed_features_dir / f"{video_id}.csv"
                if feature_file.exists():
                    val_data[video_id]['feature_frame'] = 100  # 固定100帧
                    val_data[video_id]['feature_path'] = str(feature_file)

            with open(val_file, 'w', encoding='utf-8') as f:
                json.dump(val_data, f, indent=2, ensure_ascii=False)

        print("✅ 标注文件更新完成")

    def run(self):
        """运行完整的特征提取流程"""
        print("开始为重构数据集提取SlowOnly特征...")

        # 1. 提取原始特征
        if not self.extract_raw_features():
            print("❌ 原始特征提取失败")
            return False

        # 2. 后处理特征
        if not self.postprocess_features():
            print("❌ 特征后处理失败")
            return False

        # 3. 更新标注文件
        self.update_annotation_files()

        print("✅ SlowOnly特征提取完成!")
        print(f"   原始特征保存在: {self.raw_features_dir}")
        print(f"   处理后特征保存在: {self.processed_features_dir}")

        return True

def main():
    parser = argparse.ArgumentParser(description='为重构数据集提取SlowOnly特征')
    parser.add_argument('--data-root',
                       default='/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD',
                       help='数据集根目录')
    parser.add_argument('--mmaction2-root',
                       default='/home/<USER>/johnny_ws/mmaction2_ws',
                       help='MMAction2根目录')

    args = parser.parse_args()

    extractor = SlowOnlyFeatureExtractor(
        data_root=args.data_root,
        mmaction2_root=args.mmaction2_root
    )

    success = extractor.run()
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

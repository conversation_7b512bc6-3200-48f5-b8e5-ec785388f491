#!/usr/bin/env python3
"""
简化的SlowOnly特征提取脚本
直接使用原始脚本的逻辑，为重构数据集提取特征
"""

import os
import sys
import json
import shutil
from pathlib import Path

def main():
    # 路径设置
    data_root = Path("/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD")
    feature_extraction_dir = Path("/home/<USER>/johnny_ws/mmaction2_ws/data_process/preprocessing/feature_extraction")
    
    # 1. 创建原始视频的video_list.txt
    print("1. 创建视频列表文件...")
    
    # 加载重构后的标注文件
    train_file = data_root / "multiclass_tad_train_reconstructed.json"
    val_file = data_root / "multiclass_tad_val_reconstructed.json"
    
    all_videos = {}
    
    if train_file.exists():
        with open(train_file, 'r', encoding='utf-8') as f:
            train_data = json.load(f)
            all_videos.update(train_data)
    
    if val_file.exists():
        with open(val_file, 'r', encoding='utf-8') as f:
            val_data = json.load(f)
            all_videos.update(val_data)
    
    # 创建video_list.txt文件
    video_list_file = data_root / "video_list.txt"
    
    with open(video_list_file, 'w') as f:
        for video_id, info in all_videos.items():
            if 'original_video_path' in info:
                video_path = info['original_video_path']
                if Path(video_path).exists():
                    # 使用相对路径，从raw_videos目录开始
                    video_name = Path(video_path).name
                    f.write(f"{video_name} 0\n")
                else:
                    print(f"警告: 视频文件不存在: {video_path}")
    
    print(f"视频列表已创建: {video_list_file}")
    print(f"包含 {len(all_videos)} 个视频")
    
    # 2. 运行原始的特征提取脚本
    print("2. 运行原始特征提取脚本...")
    
    # 切换到特征提取目录
    original_cwd = os.getcwd()
    os.chdir(str(feature_extraction_dir))
    
    try:
        # 运行特征提取
        import subprocess
        result = subprocess.run([
            'python', 'extract_slowonly_features.py'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 特征提取完成!")
            print("输出:", result.stdout)
        else:
            print("❌ 特征提取失败!")
            print("错误:", result.stderr)
            if result.stdout:
                print("输出:", result.stdout)
            return False
    
    except Exception as e:
        print(f"运行特征提取时出错: {e}")
        return False
    finally:
        os.chdir(original_cwd)
    
    # 3. 运行特征后处理
    print("3. 运行特征后处理...")
    
    os.chdir(str(feature_extraction_dir))
    
    try:
        result = subprocess.run([
            'python', 'postprocess_features.py'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 特征后处理完成!")
            print("输出:", result.stdout)
        else:
            print("❌ 特征后处理失败!")
            print("错误:", result.stderr)
            if result.stdout:
                print("输出:", result.stdout)
            return False
    
    except Exception as e:
        print(f"运行特征后处理时出错: {e}")
        return False
    finally:
        os.chdir(original_cwd)
    
    # 4. 复制特征文件到重构数据集目录
    print("4. 复制特征文件...")
    
    source_features_dir = data_root / "features_slowonly"
    target_features_dir = data_root / "features_slowonly_reconstructed"
    
    if source_features_dir.exists():
        # 创建目标目录
        target_features_dir.mkdir(exist_ok=True)
        
        # 复制所有CSV文件
        for csv_file in source_features_dir.glob("*.csv"):
            target_file = target_features_dir / csv_file.name
            shutil.copy2(csv_file, target_file)
            print(f"复制: {csv_file.name}")
        
        print(f"特征文件已复制到: {target_features_dir}")
    else:
        print(f"源特征目录不存在: {source_features_dir}")
        return False
    
    # 5. 更新标注文件
    print("5. 更新标注文件...")
    
    # 更新训练集
    if train_file.exists():
        with open(train_file, 'r', encoding='utf-8') as f:
            train_data = json.load(f)
        
        for video_id in train_data.keys():
            feature_file = target_features_dir / f"{Path(train_data[video_id]['original_video_path']).stem}.csv"
            if feature_file.exists():
                train_data[video_id]['feature_frame'] = 100  # SlowOnly特征固定100帧
                train_data[video_id]['feature_path'] = str(feature_file)
        
        with open(train_file, 'w', encoding='utf-8') as f:
            json.dump(train_data, f, indent=2, ensure_ascii=False)
    
    # 更新验证集
    if val_file.exists():
        with open(val_file, 'r', encoding='utf-8') as f:
            val_data = json.load(f)
        
        for video_id in val_data.keys():
            feature_file = target_features_dir / f"{Path(val_data[video_id]['original_video_path']).stem}.csv"
            if feature_file.exists():
                val_data[video_id]['feature_frame'] = 100  # SlowOnly特征固定100帧
                val_data[video_id]['feature_path'] = str(feature_file)
        
        with open(val_file, 'w', encoding='utf-8') as f:
            json.dump(val_data, f, indent=2, ensure_ascii=False)
    
    print("✅ 标注文件更新完成")
    
    # 6. 验证特征文件
    print("6. 验证特征文件...")
    
    csv_files = list(target_features_dir.glob("*.csv"))
    print(f"生成的特征文件数量: {len(csv_files)}")
    
    if csv_files:
        # 检查第一个文件的格式
        import numpy as np
        sample_file = csv_files[0]
        try:
            features = np.loadtxt(sample_file, delimiter=',')
            print(f"特征文件格式验证: {sample_file.name}")
            print(f"  特征形状: {features.shape}")
            print(f"  特征值范围: {features.min():.6f} ~ {features.max():.6f}")
            
            # 检查是否是合理的深度学习特征值
            if features.max() < 100 and features.min() > -100:
                print("  ✅ 特征值范围正常（深度学习特征）")
            else:
                print("  ⚠️ 特征值范围异常，可能不是标准的深度学习特征")
                
        except Exception as e:
            print(f"  ❌ 特征文件格式错误: {e}")
    
    print("\n✅ SlowOnly特征提取完成!")
    print(f"特征文件保存在: {target_features_dir}")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
